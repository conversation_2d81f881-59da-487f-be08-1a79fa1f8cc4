import React, { useState } from 'react';
import './SellerOnboardingStep1.css';

const SellerOnboardingStep1 = ({ onNext }) => {
  const [experiences, setExperiences] = useState([
    { school: '', position: '', from: '', to: '' },
  ]);

  const handleExperienceChange = (idx, field, value) => {
    const updated = experiences.map((exp, i) =>
      i === idx ? { ...exp, [field]: value } : exp
    );
    setExperiences(updated);
  };

  const addExperience = () => {
    setExperiences([...experiences, { school: '', position: '', from: '', to: '' }]);
  };

  return (
    <div className="seller-onboarding-step1-container max-container">
      {/* Progress Bar */}
      <div className="progress-bar">
        <div className="step active">1</div>
        <div className="progress-line" />
        <div className="step">2</div>
      </div>

      <div className="form-grid">
        {/* Description Section */}
        <div className="description-section">
          <div className="section-title">Description</div>
          <div className="description-box">
            <textarea
              className="description-textarea"
              placeholder="Write Description.."
              rows={3}
            />
            
          </div>
        </div>

        {/* Profile Pic & Experience Section */}
        <div className="profile-experience-grid">
          {/* Profile Pic */}
          <div className="profile-pic-section">
            <div className="section-title">Profile Pic</div>
            <div className="avatar-upload">
              <div className="avatar-placeholder">
                <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="32" cy="32" r="32" fill="var(--light-gray)" />
                  <ellipse cx="32" cy="27" rx="12" ry="12" fill="#fff" />
                  <ellipse cx="32" cy="50" rx="16" ry="10" fill="#fff" />
                </svg>
              </div>
              <button className="btn btn-outline upload-btn">Upload Photo</button>
            </div>
          </div>

          {/* Experience */}
          <div className="experience-section">
            <div className="section-title">Experience</div>
            {experiences.map((exp, idx) => (
              <div className="experience-row" key={idx}>
                <input
                  type="text"
                  className="input"
                  placeholder="Enter School Name"
                  value={exp.school}
                  onChange={e => handleExperienceChange(idx, 'school', e.target.value)}
                />
                <input
                  type="text"
                  className="input"
                  placeholder="Enter Position"
                  value={exp.position}
                  onChange={e => handleExperienceChange(idx, 'position', e.target.value)}
                />
                <div className="year-fields">
                  <input
                    type="text"
                    className="input year-input"
                    placeholder="From Year"
                    value={exp.from}
                    onChange={e => handleExperienceChange(idx, 'from', e.target.value)}
                  />
                  <input
                    type="text"
                    className="input year-input"
                    placeholder="To Year"
                    value={exp.to}
                    onChange={e => handleExperienceChange(idx, 'to', e.target.value)}
                  />
                </div>
              </div>
            ))}
            <div className="add-more-link" onClick={addExperience}>
              + Add More
            </div>
          </div>
        </div>
      </div>

      {/* Next Button */}
      <div className="next-btn-row">
        <button className="btn btn-primary next-btn" onClick={onNext}>Next</button>
      </div>
    </div>
  );
};

export default SellerOnboardingStep1; 