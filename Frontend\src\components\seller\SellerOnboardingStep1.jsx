import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  updateStep1Data,
  addExperience,
  updateExperience,
  removeExperience,
  updateOnboardingData,
  nextStep
} from '../../redux/slices/sellerOnboardingSlice';
import './SellerOnboardingStep1.css';

const SellerOnboardingStep1 = ({ onNext }) => {
  const dispatch = useDispatch();
  const { formData, isSaving, isError, error } = useSelector(state => state.sellerOnboarding);

  // Local state for form fields
  const [description, setDescription] = useState(formData.description || '');
  const [profilePic, setProfilePic] = useState(formData.profilePic || null);

  // Update local state when Redux state changes
  useEffect(() => {
    setDescription(formData.description || '');
    setProfilePic(formData.profilePic || null);
  }, [formData.description, formData.profilePic]);

  const handleExperienceChange = (idx, field, value) => {
    // Map field names to match backend expectations
    const fieldMap = {
      'school': 'schoolName',
      'position': 'position',
      'from': 'fromYear',
      'to': 'toYear'
    };

    const mappedField = fieldMap[field] || field;
    dispatch(updateExperience({ index: idx, field: mappedField, value }));
  };

  const addExperienceHandler = () => {
    dispatch(addExperience());
  };

  const handleProfilePicUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Create preview URL for display only
      const previewUrl = URL.createObjectURL(file);
      setProfilePic(previewUrl);
      // Note: We'll set profilePic to null in the data sent to backend
      // since we don't have file upload functionality yet
    }
  };

  const handleNext = async () => {
    // Validate required fields
    if (!description.trim()) {
      alert('Please enter a description');
      return;
    }

    if (formData.experiences.some(exp => !exp.schoolName || !exp.position)) {
      alert('Please fill in all experience fields');
      return;
    }

    // Validate year fields
    for (const exp of formData.experiences) {
      if (exp.fromYear && (isNaN(exp.fromYear) || exp.fromYear < 1900 || exp.fromYear > new Date().getFullYear())) {
        alert('Please enter valid years for experience');
        return;
      }
      if (exp.toYear && (isNaN(exp.toYear) || exp.toYear < 1900 || exp.toYear > new Date().getFullYear() + 10)) {
        alert('Please enter valid years for experience');
        return;
      }
    }

    // Convert year strings to integers and prepare data
    const processedExperiences = formData.experiences.map(exp => ({
      ...exp,
      fromYear: exp.fromYear ? parseInt(exp.fromYear, 10) : undefined,
      toYear: exp.toYear ? parseInt(exp.toYear, 10) : undefined
    }));

    // Update Redux state with current form data
    const step1Data = {
      description: description.trim(),
      // Only include profilePic if it's a valid URL (not a blob URL)
      profilePic: profilePic && !profilePic.startsWith('blob:') ? profilePic : null,
      experiences: processedExperiences
    };

    dispatch(updateStep1Data(step1Data));

    try {
      // Save step 1 data to backend
      await dispatch(updateOnboardingData(step1Data)).unwrap();

      // Move to next step
      dispatch(nextStep());
      if (onNext) onNext();
    } catch (error) {
      console.error('Error saving step 1 data:', error);

      // Show specific validation errors if available
      if (error.errors && Array.isArray(error.errors)) {
        const errorMessages = error.errors.map(err => err.msg).join('\n');
        alert(`Validation errors:\n${errorMessages}`);
      } else {
        alert('Error saving data. Please try again.');
      }
    }
  };

  return (
    <div className="seller-onboarding-step1-container max-container">
      {/* Progress Bar */}
      <div className="progress-bar">
        <div className="step active">1</div>
        <div className="progress-line" />
        <div className="step">2</div>
      </div>

      <div className="form-grid">
        {/* Description Section */}
        <div className="description-section">
          <div className="section-title">Description</div>
          <div className="description-box">
            <textarea
              className="description-textarea"
              placeholder="Write Description.."
              rows={3}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={1000}
            />
            <div className="char-count">{description.length}/1000</div>
          </div>
        </div>

        {/* Profile Pic & Experience Section */}
        <div className="profile-experience-grid">
          {/* Profile Pic */}
          <div className="profile-pic-section">
            <div className="section-title">Profile Pic (Optional)</div>
            <div className="avatar-upload">
              <div className="avatar-placeholder">
                {profilePic ? (
                  <img
                    src={profilePic}
                    alt="Profile"
                    style={{ width: '64px', height: '64px', borderRadius: '50%', objectFit: 'cover' }}
                  />
                ) : (
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="32" cy="32" r="32" fill="var(--light-gray)" />
                    <ellipse cx="32" cy="27" rx="12" ry="12" fill="#fff" />
                    <ellipse cx="32" cy="50" rx="16" ry="10" fill="#fff" />
                  </svg>
                )}
              </div>
              <input
                type="file"
                id="profile-pic-upload"
                accept="image/*"
                onChange={handleProfilePicUpload}
                style={{ display: 'none' }}
              />
              <button
                type="button"
                className="btn btn-outline upload-btn"
                onClick={() => document.getElementById('profile-pic-upload').click()}
              >
                Upload Photo
              </button>
            </div>
          </div>

          {/* Experience */}
          <div className="experience-section">
            <div className="section-title">Experience</div>
            {formData.experiences.map((exp, idx) => (
              <div className="experience-row" key={idx}>
                <input
                  type="text"
                  className="input"
                  placeholder="Enter School Name"
                  value={exp.schoolName || ''}
                  onChange={e => handleExperienceChange(idx, 'school', e.target.value)}
                />
                <input
                  type="text"
                  className="input"
                  placeholder="Enter Position"
                  value={exp.position || ''}
                  onChange={e => handleExperienceChange(idx, 'position', e.target.value)}
                />
                <div className="year-fields">
                  <input
                    type="number"
                    className="input year-input"
                    placeholder="From Year"
                    value={exp.fromYear || ''}
                    onChange={e => handleExperienceChange(idx, 'from', e.target.value)}
                    min="1900"
                    max={new Date().getFullYear()}
                  />
                  <input
                    type="number"
                    className="input year-input"
                    placeholder="To Year"
                    value={exp.toYear || ''}
                    onChange={e => handleExperienceChange(idx, 'to', e.target.value)}
                    min="1900"
                    max={new Date().getFullYear() + 10}
                  />
                </div>
                {formData.experiences.length > 1 && (
                  <button
                    type="button"
                    className="remove-experience-btn"
                    onClick={() => dispatch(removeExperience(idx))}
                    style={{ marginTop: '10px', color: 'red', background: 'none', border: 'none', cursor: 'pointer' }}
                  >
                    Remove
                  </button>
                )}
              </div>
            ))}
            <div className="add-more-link" onClick={addExperienceHandler}>
              + Add More
            </div>
          </div>
        </div>
      </div>

      {/* Next Button */}
      <div className="next-btn-row">
        <button
          className="btn btn-primary next-btn"
          onClick={handleNext}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Next'}
        </button>
      </div>

      {/* Error Display */}
      {isError && error && (
        <div className="error-message" style={{ color: 'red', textAlign: 'center', marginTop: '10px' }}>
          {error.message || 'An error occurred. Please try again.'}
        </div>
      )}
    </div>
  );
};

export default SellerOnboardingStep1;