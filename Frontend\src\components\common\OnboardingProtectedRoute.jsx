import React from 'react';
import { Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import authService from '../../services/authService';

/**
 * OnboardingProtectedRoute component
 * Redirects sellers to onboarding if they haven't completed it
 * Allows other roles to pass through normally
 */
const OnboardingProtectedRoute = ({ children }) => {
  const { user, isAuthenticated } = useSelector(state => state.auth);
  const { isOnboardingComplete, isNavigationBlocked } = useSelector(state => state.sellerOnboarding);
  
  // Get user data from Redux or localStorage
  const userData = user || authService.getStoredUser();
  const isUserAuthenticated = isAuthenticated || authService.isAuthenticated();

  // If user is not authenticated, let the main ProtectedRoute handle it
  if (!isUserAuthenticated || !userData) {
    return children;
  }

  // Only check onboarding for sellers
  if (userData.role === 'seller') {
    // If navigation is blocked (during onboarding) and user is not on onboarding page
    if (isNavigationBlocked && window.location.pathname !== '/seller-onboarding') {
      return <Navigate to="/seller-onboarding" replace />;
    }
    
    // If onboarding is not complete and user is not on onboarding page
    if (!isOnboardingComplete && window.location.pathname !== '/seller-onboarding') {
      return <Navigate to="/seller-onboarding" replace />;
    }
  }

  return children;
};

export default OnboardingProtectedRoute;
