import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import userService from '../../services/userService';
import { formatError } from '../../utils/errorHandler';

// Initial state
const initialState = {
  // Onboarding status
  isOnboardingComplete: false,
  completionPercentage: 0,
  currentStep: 1,
  
  // Form data
  formData: {
    // Step 1 data
    description: '',
    profilePic: null,
    experiences: [
      { schoolName: '', position: '', fromYear: '', toYear: '' }
    ],
    
    // Step 2 data
    minTrainingCost: '',
    socialLinks: {
      facebook: '',
      linkedin: '',
      twitter: ''
    },
    sports: [],
    expertise: [],
    certifications: []
  },
  
  // Loading states
  isLoading: false,
  isSaving: false,
  isCompleting: false,
  
  // Status flags
  isSuccess: false,
  isError: false,
  error: null,
  
  // Navigation protection
  isNavigationBlocked: false,
};

// Async thunks
export const getOnboardingStatus = createAsyncThunk(
  'sellerOnboarding/getStatus',
  async (_, thunkAPI) => {
    try {
      return await userService.getSellerOnboardingStatus();
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

export const updateOnboardingData = createAsyncThunk(
  'sellerOnboarding/updateData',
  async (onboardingData, thunkAPI) => {
    try {
      return await userService.updateSellerOnboarding(onboardingData);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

export const completeOnboarding = createAsyncThunk(
  'sellerOnboarding/complete',
  async (onboardingData, thunkAPI) => {
    try {
      return await userService.completeSellerOnboarding(onboardingData);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Slice
const sellerOnboardingSlice = createSlice({
  name: 'sellerOnboarding',
  initialState,
  reducers: {
    // Form data actions
    updateFormData: (state, action) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    
    updateStep1Data: (state, action) => {
      state.formData = {
        ...state.formData,
        description: action.payload.description || state.formData.description,
        profilePic: action.payload.profilePic || state.formData.profilePic,
        experiences: action.payload.experiences || state.formData.experiences,
      };
    },
    
    updateStep2Data: (state, action) => {
      state.formData = {
        ...state.formData,
        minTrainingCost: action.payload.minTrainingCost || state.formData.minTrainingCost,
        socialLinks: action.payload.socialLinks || state.formData.socialLinks,
        sports: action.payload.sports || state.formData.sports,
        expertise: action.payload.expertise || state.formData.expertise,
        certifications: action.payload.certifications || state.formData.certifications,
      };
    },
    
    // Experience management
    addExperience: (state) => {
      state.formData.experiences.push({
        schoolName: '',
        position: '',
        fromYear: '',
        toYear: ''
      });
    },
    
    updateExperience: (state, action) => {
      const { index, field, value } = action.payload;
      if (state.formData.experiences[index]) {
        state.formData.experiences[index][field] = value;
      }
    },
    
    removeExperience: (state, action) => {
      const index = action.payload;
      if (state.formData.experiences.length > 1) {
        state.formData.experiences.splice(index, 1);
      }
    },
    
    // Step management
    setCurrentStep: (state, action) => {
      state.currentStep = action.payload;
    },
    
    nextStep: (state) => {
      if (state.currentStep < 2) {
        state.currentStep += 1;
      }
    },
    
    previousStep: (state) => {
      if (state.currentStep > 1) {
        state.currentStep -= 1;
      }
    },
    
    // Navigation protection
    setNavigationBlocked: (state, action) => {
      state.isNavigationBlocked = action.payload;
    },
    
    // Reset actions
    resetForm: (state) => {
      state.formData = initialState.formData;
      state.currentStep = 1;
    },
    
    resetState: (state) => {
      return { ...initialState };
    },
    
    clearError: (state) => {
      state.isError = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get onboarding status
      .addCase(getOnboardingStatus.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(getOnboardingStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        
        const { data } = action.payload;
        state.isOnboardingComplete = data.isOnboardingComplete || false;
        state.completionPercentage = data.completionPercentage || 0;
        
        // Populate form data if exists
        if (data.sellerInfo) {
          state.formData = {
            ...state.formData,
            description: data.sellerInfo.description || '',
            profilePic: data.sellerInfo.profilePic || null,
            experiences: data.sellerInfo.experiences || state.formData.experiences,
            minTrainingCost: data.sellerInfo.minTrainingCost || '',
            socialLinks: data.sellerInfo.socialLinks || state.formData.socialLinks,
            sports: data.sellerInfo.sports || [],
            expertise: data.sellerInfo.expertise || [],
            certifications: data.sellerInfo.certifications || [],
          };
        }
        
        // Set navigation blocking if onboarding is not complete
        state.isNavigationBlocked = !state.isOnboardingComplete;
      })
      .addCase(getOnboardingStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Update onboarding data
      .addCase(updateOnboardingData.pending, (state) => {
        state.isSaving = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(updateOnboardingData.fulfilled, (state, action) => {
        state.isSaving = false;
        state.isSuccess = true;
        
        // Update completion percentage if provided
        if (action.payload.data && action.payload.data.completionPercentage) {
          state.completionPercentage = action.payload.data.completionPercentage;
        }
      })
      .addCase(updateOnboardingData.rejected, (state, action) => {
        state.isSaving = false;
        state.isError = true;
        state.error = action.payload;
      })
      
      // Complete onboarding
      .addCase(completeOnboarding.pending, (state) => {
        state.isCompleting = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(completeOnboarding.fulfilled, (state, action) => {
        state.isCompleting = false;
        state.isSuccess = true;
        state.isOnboardingComplete = true;
        state.completionPercentage = 100;
        state.isNavigationBlocked = false;
      })
      .addCase(completeOnboarding.rejected, (state, action) => {
        state.isCompleting = false;
        state.isError = true;
        state.error = action.payload;
      });
  },
});

// Export actions
export const {
  updateFormData,
  updateStep1Data,
  updateStep2Data,
  addExperience,
  updateExperience,
  removeExperience,
  setCurrentStep,
  nextStep,
  previousStep,
  setNavigationBlocked,
  resetForm,
  resetState,
  clearError,
} = sellerOnboardingSlice.actions;

export default sellerOnboardingSlice.reducer;
