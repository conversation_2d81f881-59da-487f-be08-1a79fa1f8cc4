.seller-onboarding-step1-container {
  width: 100%;
  min-height: 100vh;
  padding: 0rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  align-items: center;
}

.seller-onboarding-step1-container .progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  width: 50%;
}
.seller-onboarding-step1-container .step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--light-gray);
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--heading6);
  border: 2px solid var(--light-gray);
  transition: background 0.3s, color 0.3s;
}
.seller-onboarding-step1-container .step.active {
  background: var(--btn-color);
  color: var(--white);
  border: 2px solid var(--btn-color);
}
.seller-onboarding-step1-container .progress-line {
  flex: 1 1 60px;
  height: 2px;
  background: var(--light-gray);
  margin: 0 0.5rem;
}

.seller-onboarding-step1-container .form-grid {
  display: grid;
  width: 100%;
  gap: 2rem;
}

.seller-onboarding-step1-container .description-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1rem;
  position: relative;
}
.seller-onboarding-step1-container .section-title {
  font-size: var(--heading6);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  position: absolute;
  top: -20px;
  left: 50%;
  background: var(--white);
  padding: 0 0.5rem;
  transform: translate(-50%, 10px);
}
.seller-onboarding-step1-container .description-box {
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem 0.5rem 1rem;
  position: relative;
  min-height: 120px;
  display: flex;
}
.seller-onboarding-step1-container .description-textarea {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  font-size: var(--basefont);
  background: var(--white);
}

.seller-onboarding-step1-container .profile-experience-grid {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 2rem;
}
.seller-onboarding-step1-container .profile-pic-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1rem;
  position: relative;
}
.seller-onboarding-step1-container .avatar-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}
.seller-onboarding-step1-container .avatar-placeholder {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}
.seller-onboarding-step1-container .upload-btn {
  margin-top: 0.5rem;
  font-size: var(--smallfont);
  padding: 0.5rem 1.2rem;
}

.seller-onboarding-step1-container .experience-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1rem;
  position: relative;
}
.seller-onboarding-step1-container .experience-row {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}
.seller-onboarding-step1-container .input {
  flex: 1 1 120px;
  min-width: 0;
  border: 1px solid var(--light-gray);
  padding: 0.5rem 0.75rem;
  font-size: var(--basefont);
  background: var(--white);
  border-radius: var(--border-radius);
}
.seller-onboarding-step1-container .year-fields {
  display: flex;
  gap: 0.5rem;
}
.seller-onboarding-step1-container .year-input {
  width: 90px;
}
.seller-onboarding-step1-container .add-more-link {
  color: var(--btn-color);
  font-size: var(--extrasmallfont);
  cursor: pointer;
  margin-top: 0.5rem;
  align-self: flex-end;
  transition: color 0.2s;
}
.seller-onboarding-step1-container .add-more-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.seller-onboarding-step1-container .next-btn-row {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}
.seller-onboarding-step1-container .next-btn {
  min-width: 160px;
  font-size: var(--basefont);
  border-radius: var(--border-radius-large);
}

@media (max-width: 900px) {
  .seller-onboarding-step1-container .form-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  .seller-onboarding-step1-container .profile-experience-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
}

@media (max-width: 600px) {
  .seller-onboarding-step1-container {
    padding: 1rem 0.5rem;
  }
  .seller-onboarding-step1-container .form-grid {
    gap: 1rem;
  }
  .seller-onboarding-step1-container .profile-experience-grid {
    gap: 1rem;
  }
  .seller-onboarding-step1-container .next-btn {
    min-width: 120px;
    font-size: var(--smallfont);
  }
} 