import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  getOnboardingStatus,
  completeOnboarding,
  setCurrentStep,
  setNavigationBlocked
} from '../../redux/slices/sellerOnboardingSlice';
import SellerOnboardingStep1 from './SellerOnboardingStep1';
import './SellerOnboarding.css';

const SellerOnboarding = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    currentStep,
    formData,
    isOnboardingComplete,
    isCompleting,
    isLoading,
    isError,
    error
  } = useSelector(state => state.sellerOnboarding);

  // Local state for step 2 fields
  const [minCost, setMinCost] = useState(formData.minTrainingCost || '');
  const [social, setSocial] = useState(formData.socialLinks || {
    facebook: '',
    linkedin: '',
    twitter: '',
  });
  const [sports, setSports] = useState(formData.sports || []);
  const [expertise, setExpertise] = useState(formData.expertise || []);
  const [certifications, setCertifications] = useState(formData.certifications || []);

  // Available options (you can move these to constants file)
  const sportsOptions = [
    'Football', 'Basketball', 'Baseball', 'Soccer', 'Tennis', 'Golf',
    'Swimming', 'Track & Field', 'Wrestling', 'Volleyball', 'Hockey', 'Other'
  ];

  const expertiseOptions = [
    'Coaching', 'Training', 'Strategy Development', 'Player Development',
    'Team Management', 'Sports Psychology', 'Fitness Training', 'Nutrition',
    'Injury Prevention', 'Performance Analysis', 'Other'
  ];

  // Load onboarding status on component mount
  useEffect(() => {
    dispatch(getOnboardingStatus());
    dispatch(setNavigationBlocked(true)); // Block navigation during onboarding
  }, [dispatch]);

  // Update local state when Redux state changes
  useEffect(() => {
    setMinCost(formData.minTrainingCost || '');
    setSocial(formData.socialLinks || { facebook: '', linkedin: '', twitter: '' });
    setSports(formData.sports || []);
    setExpertise(formData.expertise || []);
    setCertifications(formData.certifications || []);
  }, [formData]);

  // Redirect if onboarding is already complete
  useEffect(() => {
    if (isOnboardingComplete) {
      dispatch(setNavigationBlocked(false));
      navigate('/seller/dashboard');
    }
  }, [isOnboardingComplete, navigate, dispatch]);

  const handleSocialChange = (field, value) => {
    setSocial({ ...social, [field]: value });
  };

  const handleSportsChange = (sport) => {
    setSports(prev =>
      prev.includes(sport)
        ? prev.filter(s => s !== sport)
        : [...prev, sport]
    );
  };

  const handleExpertiseChange = (exp) => {
    setExpertise(prev =>
      prev.includes(exp)
        ? prev.filter(e => e !== exp)
        : [...prev, exp]
    );
  };

  const handleCertificationAdd = (cert) => {
    if (cert.trim() && !certifications.includes(cert.trim())) {
      setCertifications([...certifications, cert.trim()]);
    }
  };

  const handleCertificationRemove = (index) => {
    setCertifications(certifications.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    // Validate required fields
    if (!minCost || parseFloat(minCost) <= 0) {
      alert('Please enter a valid minimum training cost');
      return;
    }

    if (sports.length === 0) {
      alert('Please select at least one sport');
      return;
    }

    if (expertise.length === 0) {
      alert('Please select at least one area of expertise');
      return;
    }

    // Process experiences to ensure years are integers
    const processedExperiences = formData.experiences.map(exp => ({
      ...exp,
      fromYear: exp.fromYear ? parseInt(exp.fromYear, 10) : undefined,
      toYear: exp.toYear ? parseInt(exp.toYear, 10) : undefined
    }));

    // Prepare complete onboarding data
    const completeData = {
      ...formData,
      experiences: processedExperiences,
      // Only include profilePic if it's a valid URL (not a blob URL)
      profilePic: formData.profilePic && !formData.profilePic.startsWith('blob:') ? formData.profilePic : null,
      minTrainingCost: parseFloat(minCost),
      socialLinks: social,
      sports,
      expertise,
      certifications
    };

    try {
      await dispatch(completeOnboarding(completeData)).unwrap();
      dispatch(setNavigationBlocked(false));
      navigate('/seller/dashboard');
    } catch (error) {
      console.error('Error completing onboarding:', error);

      // Show specific validation errors if available
      if (error.errors && Array.isArray(error.errors)) {
        const errorMessages = error.errors.map(err => err.msg).join('\n');
        alert(`Validation errors:\n${errorMessages}`);
      } else {
        alert('Error completing onboarding. Please try again.');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="seller-onboarding-wrapper max-container">
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <div>Loading onboarding data...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="seller-onboarding-wrapper max-container">
      {currentStep === 1 ? (
        <SellerOnboardingStep1 onNext={() => dispatch(setCurrentStep(2))} />
      ) : (
        <div className="seller-onboarding-step2-container">
          {/* Stepper */}
          <div className="progress-bar">
            <div className="step complete">1</div>
            <div className="progress-line" />
            <div className="step active">2</div>
          </div>

          {/* Minimum Customer Training Cost */}
          <div className="section-block">
            <div className="section-title">Minimum Customer Training Cost ($)</div>
            <input
              type="number"
              className="input min-cost-input"
              placeholder="Enter amount"
              value={minCost}
              onChange={e => setMinCost(e.target.value)}
              min="1"
              step="0.01"
            />
          </div>

          {/* Sports Selection */}
          <div className="section-block">
            <div className="section-title">Sports (Select at least one)</div>
            <div className="checkbox-grid">
              {sportsOptions.map(sport => (
                <label key={sport} className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={sports.includes(sport)}
                    onChange={() => handleSportsChange(sport)}
                  />
                  <span>{sport}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Expertise Selection */}
          <div className="section-block">
            <div className="section-title">Areas of Expertise (Select at least one)</div>
            <div className="checkbox-grid">
              {expertiseOptions.map(exp => (
                <label key={exp} className="checkbox-item">
                  <input
                    type="checkbox"
                    checked={expertise.includes(exp)}
                    onChange={() => handleExpertiseChange(exp)}
                  />
                  <span>{exp}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Social Media Account */}
          <div className="section-block">
            <div className="section-title">Social Media Account (Optional)</div>
            <div className="social-inputs-grid">
              <div className="social-input-row">
                <span className="social-icon facebook">
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><path d="M18 10A8 8 0 1 0 10 18V12.89H8.1V10.89H10V9.22C10 7.5 11.17 6.5 12.72 6.5C13.44 6.5 14.2 6.62 14.2 6.62V8.6H13.23C12.27 8.6 12 9.18 12 9.77V10.89H14.1L13.8 12.89H12V18A8 8 0 0 0 18 10Z" fill="#1877F3"/><path d="M13.8 12.89L14.1 10.89H12V9.77C12 9.18 12.27 8.6 13.23 8.6H14.2V6.62S13.44 6.5 12.72 6.5C11.17 6.5 10 7.5 10 9.22V10.89H8.1V12.89H10V18C10.67 18 11.32 17.93 11.95 17.8V12.89H13.8Z" fill="#fff"/></svg>
                </span>
                <input
                  type="url"
                  className="input social-input"
                  placeholder="Facebook URL"
                  value={social.facebook}
                  onChange={e => handleSocialChange('facebook', e.target.value)}
                />
              </div>
              <div className="social-input-row">
                <span className="social-icon linkedin">
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="#0A66C2"/><path d="M6.94 8.5H4.98V15H6.94V8.5ZM5.96 7.5C6.6 7.5 7.1 7 7.1 6.36C7.1 5.72 6.6 5.22 5.96 5.22C5.32 5.22 4.82 5.72 4.82 6.36C4.82 7 5.32 7.5 5.96 7.5ZM15 15H13.04V11.5C13.04 10.67 12.37 10 11.54 10C10.71 10 10.04 10.67 10.04 11.5V15H8.08V8.5H10.04V9.38C10.41 8.81 11.13 8.5 11.54 8.5C13.01 8.5 15 9.44 15 11.5V15Z" fill="#fff"/></svg>
                </span>
                <input
                  type="url"
                  className="input social-input"
                  placeholder="LinkedIn URL"
                  value={social.linkedin}
                  onChange={e => handleSocialChange('linkedin', e.target.value)}
                />
              </div>
              <div className="social-input-row">
                <span className="social-icon twitter">
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="#1DA1F2"/><path d="M15.32 8.13C15.33 8.23 15.33 8.33 15.33 8.43C15.33 11.13 13.29 14.13 9.5 14.13C8.37 14.13 7.31 13.8 6.4 13.23C6.56 13.25 6.72 13.26 6.89 13.26C7.82 13.26 8.66 12.95 9.36 12.44C8.48 12.43 7.74 11.87 7.49 11.07C7.62 11.09 7.75 11.11 7.89 11.11C8.08 11.11 8.27 11.08 8.45 11.03C7.54 10.85 6.85 10.03 6.85 9.06V9.04C7.11 9.19 7.42 9.28 7.75 9.29C7.19 8.91 6.81 8.28 6.81 7.57C6.81 7.23 6.9 6.92 7.07 6.66C8.04 7.84 9.47 8.62 11.07 8.7C11.04 8.56 11.03 8.41 11.03 8.27C11.03 7.29 11.82 6.5 12.8 6.5C13.29 6.5 13.73 6.7 14.03 7.04C14.4 6.97 14.75 6.85 15.06 6.68C14.95 7.06 14.7 7.37 14.37 7.56C14.7 7.53 15.01 7.44 15.32 7.3C15.06 7.62 14.72 7.89 14.34 8.13H15.32Z" fill="#fff"/></svg>
                </span>
                <input
                  type="url"
                  className="input social-input"
                  placeholder="Twitter URL"
                  value={social.twitter}
                  onChange={e => handleSocialChange('twitter', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Certifications */}
          <div className="section-block">
            <div className="section-title">Certifications (Optional)</div>
            <div className="certifications-container">
              {certifications.map((cert, index) => (
                <div key={index} className="certification-item">
                  <span>{cert}</span>
                  <button
                    type="button"
                    onClick={() => handleCertificationRemove(index)}
                    className="remove-cert-btn"
                    style={{ marginLeft: '10px', color: 'red', background: 'none', border: 'none', cursor: 'pointer' }}
                  >
                    ×
                  </button>
                </div>
              ))}
              <div className="add-certification">
                <input
                  type="text"
                  className="input"
                  placeholder="Add certification"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleCertificationAdd(e.target.value);
                      e.target.value = '';
                    }
                  }}
                />
                <button
                  type="button"
                  className="btn btn-outline"
                  onClick={(e) => {
                    const input = e.target.previousElementSibling;
                    handleCertificationAdd(input.value);
                    input.value = '';
                  }}
                >
                  Add
                </button>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="next-btn-row">
            <button
              className="btn btn-primary next-btn"
              onClick={handleSubmit}
              disabled={isCompleting}
            >
              {isCompleting ? 'Completing...' : 'Complete Onboarding'}
            </button>
          </div>

          {/* Error Display */}
          {isError && error && (
            <div className="error-message" style={{ color: 'red', textAlign: 'center', marginTop: '10px' }}>
              {error.message || 'An error occurred. Please try again.'}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SellerOnboarding;