.seller-onboarding-wrapper {
  width: 100%;
  margin: 0 auto;
  background: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  min-height: 100vh;
}

.seller-onboarding-wrapper .seller-onboarding-step2-container {
  display: grid;
  justify-items: center;
  gap: 2rem;
  align-items: center;
  width: 100%;
}

.seller-onboarding-wrapper .progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  width: 50%;
}
.seller-onboarding-wrapper .step {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--light-gray);
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--heading6);
  border: 2px solid var(--light-gray);
  transition: background 0.3s, color 0.3s;
}
.seller-onboarding-wrapper .step.active {
  background: var(--btn-color);
  color: var(--white);
  border: 2px solid var(--btn-color);
}
.seller-onboarding-wrapper .step.complete {
  background: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}
.seller-onboarding-wrapper .progress-line {
  flex: 1 1 60px;
  height: 2px;
  background: var(--light-gray);
  margin: 0 0.5rem;
}

.seller-onboarding-wrapper .section-block {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: 1.5rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
  width: 100%;
}
.seller-onboarding-wrapper .section-title {
  font-size: var(--heading6);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  position: absolute;
  top: -20px;
  left: 50%;
  background: var(--white);
  padding: 0 0.5rem;
  transform: translate(-50%, 10px);
}
.seller-onboarding-wrapper .min-cost-input {
  width: 100%;
  min-width: 0;
    padding: 0.5rem 0.75rem;
    font-size: var(--basefont);
    background: var(--white);
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
}

.seller-onboarding-wrapper .social-inputs-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.seller-onboarding-wrapper .social-input-row {
  display: flex;
  align-items: center;

  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);

}
.seller-onboarding-wrapper .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--white);
  box-shadow: var(--box-shadow-light);
}
.seller-onboarding-wrapper .social-icon.facebook svg {
  display: block;
}
.seller-onboarding-wrapper .social-icon.linkedin svg {
  display: block;
}
.seller-onboarding-wrapper .social-icon.twitter svg {
  display: block;
}
.seller-onboarding-wrapper .social-input {
  flex: 1 1 200px;
  min-width: 0;
  padding: 0.5rem 0.75rem;
  font-size: var(--basefont);
  background: var(--white);
  border-top: none;
  border-right: none;
  border-bottom: none;
  border-left: 1px solid var(--light-gray);
}

.seller-onboarding-wrapper .next-btn-row {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}
.seller-onboarding-wrapper .next-btn {
  min-width: 160px;
  font-size: var(--basefont);
  border-radius: var(--border-radius-large);
}

/* Checkbox Grid Styles */
.seller-onboarding-wrapper .checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.seller-onboarding-wrapper .checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.seller-onboarding-wrapper .checkbox-item:hover {
  background-color: var(--light-gray);
  border-color: var(--primary-color);
}

.seller-onboarding-wrapper .checkbox-item input[type="checkbox"] {
  margin: 0;
  cursor: pointer;
}

.seller-onboarding-wrapper .checkbox-item span {
  font-size: var(--basefont);
  color: var(--secondary-color);
  cursor: pointer;
}

/* Certifications Styles */
.seller-onboarding-wrapper .certifications-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.seller-onboarding-wrapper .certification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
}

.seller-onboarding-wrapper .certification-item span {
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.seller-onboarding-wrapper .add-certification {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.seller-onboarding-wrapper .add-certification input {
  flex: 1;
  padding: 0.5rem 0.75rem;
  font-size: var(--basefont);
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.seller-onboarding-wrapper .add-certification button {
  padding: 0.5rem 1rem;
  font-size: var(--basefont);
  border-radius: var(--border-radius);
}

/* Character count styles */
.seller-onboarding-wrapper .char-count {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  text-align: right;
  margin-top: 0.25rem;
}

/* Error message styles */
.seller-onboarding-wrapper .error-message {
  padding: 0.75rem;
  background-color: #fee;
  border: 1px solid #fcc;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
}

@media (max-width: 900px) {
  .seller-onboarding-wrapper {
    padding: 1rem 0.5rem;
  }
}
@media (max-width: 600px) {
  .seller-onboarding-wrapper {
    padding: 0.5rem 0.2rem;
  }
  .seller-onboarding-wrapper .section-block {
    padding: 1rem 0.5rem;
  }
  .seller-onboarding-wrapper .next-btn {
    min-width: 120px;
    font-size: var(--smallfont);
  }
}